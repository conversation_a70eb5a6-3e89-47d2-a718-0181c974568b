# RUST-SS Agent Communication Database

## Overview

This document describes the location and access methods for the RUST-SS agent communication database, which stores coordination data from the documentation build project migration.

## Database Migration Summary

The RUST-SS agent coordination system has been successfully migrated from a JSON-based temporary system to a persistent **basic-memory** knowledge management database. This migration transformed agent coordination data into a searchable, semantic knowledge graph.

### Migration Details
- **Source**: JSON files in `shared-memory/` directory (removed after migration)
- **Destination**: Basic-memory SQLite database with semantic relationships
- **Data Preserved**: 100% of agent coordination information
- **Format**: Searchable knowledge graph with observations and relations

## Database Location

### Primary Database
```
📍 Location: /home/<USER>/.basic-memory/memory.db
📊 Size: 561 KB (561,152 bytes)
📋 Type: SQLite database with full-text search
🔗 Project: rust-swarm-agent-communication
```

### Configuration Files
```
📍 Config: /home/<USER>/.basic-memory/config.json
📝 Logs: /home/<USER>/.basic-memory/basic-memory-*.log
👁️  Status: /home/<USER>/.basic-memory/watch-status.json
```

## Accessing the Database

### Method 1: Symbolic Link (Recommended)
The database is accessible via a symbolic link in this directory:

```
📁 RUST-SS/basic-memory-database/ → /home/<USER>/.basic-memory/
```

**To access in VS Code:**
1. Navigate to `/workspaces/claude-code-flow/RUST-SS/`
2. Open `basic-memory-database/` folder
3. View `memory.db` and other files

### Method 2: Direct Path Access
```bash
# View database files
ls -la /home/<USER>/.basic-memory/

# Query database directly
sqlite3 /home/<USER>/.basic-memory/memory.db "SELECT title FROM entity;"
```

### Method 3: Basic-Memory CLI
```bash
# Search agent data
basic-memory search "agent coordination"

# Read specific documents
basic-memory read "RUST-SS Documentation Build Project"

# List all projects
basic-memory project list
```

## Database Contents

### Migrated Data Structure
The database contains a complete semantic representation of the RUST-SS agent coordination system:

```
📊 Statistics:
├── 7 Main Entities (core documents)
├── 47 Observations (categorized facts with tags)
└── 34 Relations (semantic relationships)
```

### Core Entities
1. **RUST-SS Documentation Build Project** - Main project overview
2. **Phase 1 - Directory Structure Creation** - Foundation phase (100% complete)
3. **Phase 2 - Implementation Documentation** - Documentation phase (95% complete)  
4. **Phase 3 - Rust Code Conversion** - Conversion phase (65% complete)
5. **Agent Registry** - 25 agents tracked across all phases
6. **Coordination Protocol** - Communication rules and procedures
7. **Individual Agent Profiles** - Key agents with performance metrics

### Semantic Relationships
The knowledge graph includes relationships such as:
- Project `consists_of` Phases
- Phases `part_of` Project
- Agents `assigned_to` Phases
- Protocols `coordinate` Agents
- Registry `manages` Agents

## Project Configuration

### Basic-Memory Project Settings
```json
{
  "projects": {
    "rust-swarm-agent-communication": "/workspaces/claude-code-flow/RUST-SS/agent-communication"
  },
  "default_project": "rust-swarm-agent-communication"
}
```

### MCP Integration
The database is accessible through Claude Code via the basic-memory MCP server:
- **MCP Tools**: search_notes, read_note, write_note, build_context
- **Search Capability**: Full-text search across all agent coordination data
- **Context Building**: Semantic relationship traversal

## Data Backup & Transfer

### For Backup
To backup the agent coordination database:
```bash
# Copy database file
cp /home/<USER>/.basic-memory/memory.db ./rust-ss-agent-db-backup.db

# Copy configuration
cp /home/<USER>/.basic-memory/config.json ./rust-ss-config-backup.json
```

### For Transfer to Another System
1. **Copy database**: `/home/<USER>/.basic-memory/memory.db`
2. **Copy config**: `/home/<USER>/.basic-memory/config.json`
3. **Update paths** in config.json for new system location
4. **Install basic-memory**: `pip install basic-memory --pre`
5. **Add project**: `basic-memory project add rust-swarm-agent-communication <new-path>`

### For Download from Codespace
1. **Right-click** on `basic-memory-database/memory.db` in VS Code
2. **Select "Download"** to save locally
3. **Also download** `config.json` to preserve settings

## Key Features

### Search Capabilities
```bash
# Find all agent-related data
basic-memory search "agent coordination"

# Find specific phase information  
basic-memory search "Phase 2"

# Find performance metrics
basic-memory search "completion time"
```

### Relationship Traversal
The semantic graph enables following relationships:
- From Project → to Phases → to Agents
- From Agents → to Coordination Protocol → to Communication Rules
- From Phases → to Task Progress → to Success Metrics

### Historical Context
All agent coordination data is preserved with:
- Agent performance metrics (completion times, tool usage, token consumption)
- Phase progression tracking (85% overall completion)
- Coordination protocol evolution
- Success rates and failure analysis

## Benefits of Basic-Memory Migration

1. **Persistent Storage**: Survives disconnections and restarts
2. **Searchable**: Full-text search across all coordination data
3. **Semantic**: Rich relationship graph for navigation
4. **AI Accessible**: Available through MCP tools for future agents
5. **Human Readable**: Markdown-based format for direct editing
6. **Version Control**: Standard files compatible with git
7. **Scalable**: Can grow to include additional agent operations

## Usage in Future Agent Operations

This database serves as a knowledge base for future RUST-SS agent swarms:
- **Historical Context**: Learn from previous agent coordination patterns
- **Performance Baselines**: Compare against 25-agent deployment metrics
- **Protocol Templates**: Reuse established coordination procedures
- **Success Patterns**: Reference what worked in Phase 2 (95% success rate)
- **Risk Mitigation**: Learn from Phase 3 connection error issues

## Technical Notes

- **Database Engine**: SQLite with FTS (Full-Text Search)
- **Access Method**: Basic-memory MCP server
- **Query Language**: Natural language search + SQL when needed
- **Backup Strategy**: File-based (memory.db + config.json)
- **Integration**: Claude Code MCP tools for AI agent access

---

**Last Updated**: June 30, 2025  
**Database Version**: 1.0  
**Migration Status**: Complete ✅  
**Total Records**: 7 entities, 47 observations, 34 relations