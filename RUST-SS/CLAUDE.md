# RUST-SS Agent Guide & Documentation Framework

**Purpose**: This is a comprehensive documentation reference framework for building a Rust-based swarm system. This directory contains documentation specifications and architectural patterns - NOT the actual system code. Agents use this documentation to understand requirements and build the real RUST-SS system.

## Quick Start: Basic-Memory MCP Commands

### Essential Commands for Agent Coordination
```bash
# Search for project context
mcp__basic-memory__search_notes {"query": "RUST-SS project coordination"}

# Store findings and progress
mcp__basic-memory__write_note {
  "title": "Agent Task Progress",
  "content": "## Findings\n...",
  "folder": "coordination",
  "tags": ["agent-work", "progress"]
}

# Build context from previous work
mcp__basic-memory__build_context {"url": "memory://coordination/agent-task-progress"}

# Read specific documentation
mcp__basic-memory__read_note {"identifier": "RUST-SS Documentation Build Project"}
```

### Current Project Context
- **Project**: `rust-swarm-agent-communication` (default basic-memory project)
- **Coordination Database**: Contains 25+ agent coordination records
- **Status**: Documentation framework complete, implementation phase ready

## Configuration Data (JSON)

### SPARC Modes Configuration
```json
{
  "sparc_modes": {
    "orchestrator": {
      "purpose": "Multi-agent coordination and task distribution",
      "capabilities": ["task_delegation", "agent_management", "workflow_coordination"],
      "use_cases": ["complex_projects", "multi_phase_development"],
      "coordination_pattern": "centralized"
    },
    "researcher": {
      "purpose": "Information gathering and analysis",
      "capabilities": ["data_collection", "trend_analysis", "documentation_review"],
      "use_cases": ["requirement_analysis", "technology_research"],
      "coordination_pattern": "distributed"
    },
    "coder": {
      "purpose": "Implementation and development",
      "capabilities": ["code_generation", "refactoring", "optimization"],
      "use_cases": ["feature_development", "bug_fixes"],
      "coordination_pattern": "hierarchical"
    },
    "architect": {
      "purpose": "System design and architectural decisions",
      "capabilities": ["system_design", "pattern_selection", "scalability_planning"],
      "use_cases": ["system_architecture", "design_patterns"],
      "coordination_pattern": "centralized"
    },
    "tester": {
      "purpose": "Quality assurance and testing",
      "capabilities": ["test_design", "validation", "performance_testing"],
      "use_cases": ["quality_validation", "regression_testing"],
      "coordination_pattern": "distributed"
    }
  }
}
```

### MCP Command Patterns
```json
{
  "common_patterns": {
    "start_work_session": [
      "mcp__basic-memory__search_notes {\"query\": \"current project status\"}",
      "mcp__basic-memory__build_context {\"url\": \"memory://projects/rust-ss-documentation-build-project\"}"
    ],
    "store_progress": [
      "mcp__basic-memory__write_note {\"title\": \"[Agent-ID] Progress Update\", \"folder\": \"progress\", \"content\": \"## Completed\\n...\\n## Next Steps\\n...\"}",
      "mcp__basic-memory__edit_note {\"identifier\": \"project-status\", \"operation\": \"append\", \"content\": \"- [timestamp] Agent progress update\"}"
    ],
    "coordinate_with_team": [
      "mcp__basic-memory__search_notes {\"query\": \"agent coordination current phase\"}",
      "mcp__basic-memory__write_note {\"title\": \"Coordination Request\", \"folder\": \"coordination\", \"tags\": [\"request\", \"collaboration\"]}"
    ]
  }
}
```

### Directory Navigation Index
```json
{
  "directory_index": {
    "concepts/": {
      "purpose": "Core system concepts and patterns",
      "key_files": ["agent-spawning/", "memory-sharing/", "session-management/"],
      "priority": "high",
      "agent_relevance": "foundational_understanding"
    },
    "features/sparc-modes/": {
      "purpose": "Detailed SPARC mode implementations",
      "key_files": ["orchestrator/", "coder/", "researcher/", "architect/"],
      "priority": "high",
      "agent_relevance": "mode_specific_guidance"
    },
    "architecture/": {
      "purpose": "System architecture and design patterns",
      "key_files": ["system-design/", "scalability/", "security/"],
      "priority": "medium",
      "agent_relevance": "implementation_guidance"
    },
    "coordination-modes/": {
      "purpose": "Agent coordination strategies",
      "key_files": ["centralized/", "distributed/", "hierarchical/", "mesh/"],
      "priority": "high",
      "agent_relevance": "coordination_patterns"
    },
    "services/": {
      "purpose": "Service architecture and implementation",
      "key_files": ["agent-management/", "coordination/", "memory/"],
      "priority": "medium",
      "agent_relevance": "service_implementation"
    }
  }
}
```

## Documentation Framework Overview

This RUST-SS directory is a **documentation reference framework** designed to guide the development of a Rust-based swarm system. It serves as:

### Framework Purpose
- **Reference Specification**: Detailed documentation of system requirements and patterns
- **Implementation Guide**: Instructions for building the actual RUST-SS system
- **Agent Coordination**: Structure for agent collaboration during development
- **Architecture Blueprint**: Comprehensive system design and component specifications

### What This Framework Contains
- **Conceptual Documentation**: Core ideas, patterns, and approaches
- **Technical Specifications**: Detailed implementation requirements
- **Coordination Patterns**: Multi-agent collaboration strategies
- **Integration Guides**: How components work together
- **Best Practices**: Proven patterns and optimization strategies

### What This Framework Does NOT Contain
- **Actual System Code**: No executable Rust implementation files
- **Runtime Components**: No running services or applications
- **Data Storage**: No production databases or persistent state
- **Deployment Artifacts**: No compiled binaries or containers

## Directory Structure & Navigation

### Core Documentation Areas

#### `/concepts/` - Foundational Concepts
- **Purpose**: Core system concepts that underpin the entire framework
- **Contents**: Agent spawning, memory sharing, session management, state persistence
- **Agent Usage**: Start here for foundational understanding before implementation

#### `/features/` - Feature Specifications
- **Purpose**: Detailed specifications for system capabilities
- **Key Sections**:
  - `sparc-modes/`: 17 specialized agent modes with implementation details
  - `swarm-strategies/`: Multi-agent coordination strategies
  - `coordination-modes/`: Different approaches to agent coordination
- **Agent Usage**: Reference for implementing specific features and capabilities

#### `/architecture/` - System Architecture
- **Purpose**: High-level system design and architectural patterns
- **Contents**: Data models, deployment strategies, interfaces, security patterns
- **Agent Usage**: Understand system structure before detailed implementation

#### `/services/` - Service Documentation
- **Purpose**: Detailed service specifications and interfaces
- **Key Services**: Agent management, coordination, memory, communication hub
- **Agent Usage**: Implementation guide for individual system services

#### `/coordination-modes/` - Coordination Strategies
- **Purpose**: Different approaches to multi-agent coordination
- **Modes**: Centralized, distributed, hierarchical, mesh, hybrid
- **Agent Usage**: Choose appropriate coordination pattern for specific tasks

#### `/cli/` - Command Line Interface
- **Purpose**: CLI design patterns and command structures
- **Contents**: Argument parsing, command chaining, session management
- **Agent Usage**: Implement command-line interface components

#### `/integration/` - Integration Patterns
- **Purpose**: How system components integrate with external systems
- **Contents**: API management, data synchronization, event streaming
- **Agent Usage**: Implement external system integrations

#### `/infrastructure/` - Infrastructure Specifications
- **Purpose**: System infrastructure requirements and patterns
- **Contents**: Caching, configuration, messaging, monitoring, persistence
- **Agent Usage**: Implement infrastructure and operational components

## Agent Coordination Patterns

### Basic-Memory Usage for Team Coordination

#### 1. Project Context Sharing
```markdown
# Store project findings for team access
mcp__basic-memory__write_note {
  "title": "Architecture Decision Record",
  "folder": "decisions",
  "content": "## Decision\n...\n## Rationale\n...\n## Impact\n...",
  "tags": ["adr", "architecture", "team-decision"]
}
```

#### 2. Progress Tracking
```markdown
# Update team on work progress
mcp__basic-memory__write_note {
  "title": "Sprint Progress - Agent [ID]",
  "folder": "progress",
  "content": "## Completed\n- Feature X implementation\n## In Progress\n- Feature Y design\n## Blocked\n- Waiting for decision on Z",
  "tags": ["progress", "sprint", "status"]
}
```

#### 3. Knowledge Sharing
```markdown
# Share discoveries and learnings
mcp__basic-memory__write_note {
  "title": "Implementation Patterns - [Component]",
  "folder": "knowledge",
  "content": "## Pattern\n...\n## Benefits\n...\n## Usage\n...",
  "tags": ["pattern", "knowledge", "implementation"]
}
```

### Coordination Protocols

#### Agent Identification
- **Format**: `[SPARC-Mode]-[Specialization]-[Session-ID]`
- **Example**: `coder-rust-backend-12345`

#### Status Updates
- **Frequency**: At task completion or major milestones
- **Location**: `progress/` folder in basic-memory
- **Format**: Structured markdown with completed/in-progress/blocked sections

#### Decision Records
- **Location**: `decisions/` folder
- **Format**: ADR (Architecture Decision Record) template
- **Sharing**: Tag with relevant team members and components

## Common Workflows & Examples

### Starting a New Implementation Task

1. **Gather Context**
```bash
mcp__basic-memory__search_notes {"query": "current implementation status"}
mcp__basic-memory__build_context {"url": "memory://projects/rust-ss-documentation-build-project"}
```

2. **Review Requirements**
- Navigate to relevant `/features/` or `/services/` documentation
- Read CLAUDE.md files for specific components
- Check existing implementation patterns

3. **Document Approach**
```bash
mcp__basic-memory__write_note {
  "title": "Implementation Plan - [Component]",
  "folder": "plans",
  "content": "## Approach\n...\n## Dependencies\n...\n## Timeline\n..."
}
```

4. **Execute and Update**
- Implement according to documentation specifications
- Store progress updates in basic-memory
- Share findings and patterns with team

### Coordinating with Other Agents

1. **Check Team Status**
```bash
mcp__basic-memory__search_notes {"query": "agent progress current phase"}
```

2. **Share Work Context**
```bash
mcp__basic-memory__write_note {
  "title": "Coordination - [Your-Work] needs [Other-Work]",
  "folder": "coordination",
  "tags": ["dependency", "coordination", "urgent"]
}
```

3. **Monitor Dependencies**
```bash
mcp__basic-memory__search_notes {"query": "coordination dependency"}
```

### Framework Navigation Tips

1. **Start with Overview**: Read main CLAUDE.md files in each major directory
2. **Follow Dependencies**: Use the Relations sections in basic-memory notes
3. **Search Effectively**: Use specific terms like "coordination", "implementation", "pattern"
4. **Build Context**: Use `build_context` for comprehensive understanding of related work
5. **Stay Updated**: Regular search for "progress" and "status" updates from team

---

**Remember**: This is a documentation framework for building the actual RUST-SS system. Your role is to implement the specifications and patterns documented here, not to modify the documentation itself unless specifically tasked with documentation updates.