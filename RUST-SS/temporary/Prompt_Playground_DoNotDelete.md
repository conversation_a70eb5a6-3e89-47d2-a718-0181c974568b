Here is how I want the agents coordinated. In the initial conversation, I will input the prompt, the main agent inside the conversation (that is not spawnned) will be the main coordinator. It will    │
│   setup the main coordination structure for the rest of the swarms/groups. After this is done, in the same ongoing response, it will think and then launch a 4 agent swarm to work in parallel with each  │
│   other on analyzing the entire structure (files, folders, maximum depth) of the @RUST-SS/ directory stating the purpose of each file and each folder (except for the agent-comms folder and the          │
│   CLAUDE.md files). This swarm/group WILL NOT perform any actions other than reading and analyzing. This is because you can't identify any patterns of redundency if they havent compared their findings  │
│   to one another.\                                                                                                                                                                                        │
│   \                                                                                                                                                                                                       │
│   After that group gets done, the main conversational/coordinator agent will ensure the groups reports and findings are in-tact, and then analyze the reports created by the agents, identify areas of    │
│   redundency, and then collaborate with other models via zen thoroughly to verify that its identification of redundencies and overengineering, etc. are indeed correct. It's important to note that the   │
│   zen models might not have access to the individual agent reports that are needed  in order to verify the coordinators identifying areas/report so its important that the main coordinator/conversation  │
│   agent provide the zen mcp agents with all the information or files locations so they could see if anything is missing from the main coordinators report.\                                               │
│   \                                                                                                                                                                                                       │
│   After 