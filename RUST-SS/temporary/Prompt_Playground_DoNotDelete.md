# RUST-SS Documentation Redundancy Analysis - Optimized Agent Swarm Prompt

## Mission Statement
Conduct a comprehensive analysis of the RUST-SS documentation framework to identify redundancy, over-engineering, and anti-patterns. Deploy a multi-phase agent swarm approach with coordination, verification, and restructuring planning phases.

## Critical Context & Exclusions

### CLAUDE.md Files - NOT Redundant
**IMPORTANT**: CLAUDE.md files are a Claude Code best practice for providing directory-specific context to AI agents. These files are intentionally placed throughout the directory structure and should NOT be considered redundant. Each CLAUDE.md serves a unique purpose for AI agent navigation and should be excluded from redundancy analysis.

### Agent-Comms Folder
The `agent-comms/` folder should be excluded from analysis as it contains active coordination infrastructure.

## Phase 1: Initial Analysis Swarm (3-7 Agents)

### Coordinator Setup
The main conversational agent (non-spawned) serves as the primary coordinator and will:
- Establish coordination structure using basic-memory
- Determine optimal agent count (3-7) based on directory complexity
- Launch parallel analysis swarm
- Monitor progress and ensure phase completion

### Analysis Swarm Objectives
Deploy agents to analyze the entire RUST-SS directory structure (maximum depth) with focus on:

**Primary Analysis Areas:**
- File and folder purpose identification
- Structural pattern recognition
- Content similarity detection
- Organizational hierarchy assessment

**Key Redundancy Indicators to Investigate:**
- Duplicate directory structures in different locations
- Similar file patterns across parallel directories
- Repeated configuration/implementation patterns
- Over-engineered abstractions with minimal differentiation
- Scattered related concepts across distant directories

**Anti-Patterns to Detect:**
- Excessive directory nesting (>4 levels) for simple concepts
- Single-file directories without clear purpose
- Inconsistent organization of similar concepts
- Premature abstraction creating unnecessary complexity

### Agent Constraints
**CRITICAL**: This swarm performs READ-ONLY analysis. No modifications, deletions, or restructuring actions. Agents must compare findings collaboratively to identify patterns.

## Phase 2: Coordination and Verification

### Main Coordinator Responsibilities
1. **Findings Integration**: Collect and analyze all agent reports
2. **Pattern Identification**: Identify redundancy and over-engineering patterns
3. **Report Generation**: Create comprehensive findings report in both:
   - Temporary file: `@temporary/rust-ss-redundancy-analysis.md`
   - Basic-memory entry for persistence
4. **Zen Collaboration**: Verify findings through zen model collaboration
   - Share coordinator report AND individual agent reports
   - Provide complete context for verification
   - Iterate based on zen feedback until verified
5. **Report Finalization**: Update basic-memory with verified report, clean up temporary files

### Verification Protocol
- Zen models receive full context (coordinator + agent reports)
- Multiple verification rounds if needed
- Final report must be zen-verified before proceeding

## Phase 3: Restructuring Planning

### Dynamic Swarm Deployment
Based on verified redundancy analysis, launch appropriately-sized agent swarm to:
- Create detailed restructuring plan
- Prioritize consolidation opportunities
- Design optimized directory structure
- Provide implementation roadmap

### Plan Verification
- Coordinator self-verification using ultrathinking and code-reasoning
- Zen collaboration for plan validation
- Iterative refinement until verified

### Swarm Strategy Optimization
Coordinator develops optimized strategy for potential execution phase agents.

## Basic-Memory Integration

### Project Structure
```
/analysis/rust-ss-redundancy/
├── phase1-findings/
│   ├── agent-reports/
│   └── consolidated-analysis/
├── verification/
│   ├── coordinator-report/
│   └── zen-feedback/
└── restructuring-plan/
    ├── recommendations/
    └── implementation-strategy/
```

### Communication Protocol
- Use basic-memory for persistent findings storage
- Tag entries with phase, agent-id, and analysis type
- Enable cross-agent pattern detection through shared context
- Maintain audit trail of verification steps

## Output Expectations

### Flexible Report Structure
Reports should adapt to findings rather than follow rigid templates. Consider including:
- **Executive Summary**: Key redundancy patterns identified
- **Quantitative Analysis**: Measurable redundancy metrics where applicable
- **Pattern Documentation**: Systemic issues across the framework
- **Prioritized Recommendations**: Impact-based improvement suggestions
- **Visual Representations**: Structure comparisons or helpful diagrams

### Success Criteria
- Complete RUST-SS directory coverage (excluding agent-comms and CLAUDE.md analysis)
- Evidence-based redundancy identification
- Actionable consolidation recommendations
- Verified findings through zen collaboration
- Clear restructuring roadmap

## Execution Guidance

### Starting Point Context
The RUST-SS framework contains:
- Multiple coordination-modes implementations
- 17+ SPARC mode variations
- Distributed services and infrastructure patterns
- Feature hierarchies with potential overlap

### Analysis Focus Areas
- Coordination patterns appearing in multiple locations
- SPARC modes with similar structures
- Service patterns with repeated implementations
- Documentation organization inconsistencies

### Quality Assurance
- Evidence-based findings only
- Collaborative pattern verification
- Zen model validation at key checkpoints
- Iterative refinement based on feedback

This prompt provides the framework and context for comprehensive redundancy analysis while allowing agents the flexibility to adapt their approach based on actual findings.