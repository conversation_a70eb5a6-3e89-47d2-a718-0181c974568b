# Agent Coordination Strategy

## Overview
Here is how I want the agents coordinated. In the initial conversation, I will input the prompt, the main agent inside the conversation (that is not spawned) will be the main coordinator. It will setup the main coordination structure for the rest of the swarms/groups.

## Phase 1: Initial Analysis Swarm
After this is done, in the same ongoing response, it will think and then launch a **4 agent swarm** to work in parallel with each other on analyzing the entire structure (files, folders, maximum depth) of the `@RUST-SS/` directory stating the purpose of each file and each folder (except for the agent-comms folder and the CLAUDE.md files).

**Important:** This swarm/group WILL NOT perform any actions other than reading and analyzing. This is because you can't identify any patterns of redundancy if they haven't compared their findings to one another.

## Phase 2: Coordination and Verification
After that group gets done, the main conversational/coordinator agent will:

1. Ensure the group's reports and findings are intact
2. Analyze the reports created by the agents
3. Identify areas of redundancy and overengineering
4. Create a report of its findings in a file inside @temporary/ and as a basic-memory entry
4. Collaborate with other models via zen thoroughly to verify that its identification of redundancies and overengineering, etc. are indeed correct by sharing the report it just created and the agents reports in order to compare the two documents and verify the main coordinators report is not missing anything.
5. If zen finds any issues or areas that need to be added to the report, the main coordinator/conversation agent will update the report and re-share it with zen for verification.
6. Once zen verifies the report is correct, the main coordinator/conversation agent will update the same report entryin basic-memory. If it is unable to update the entry, it will create a new one and then delete the old one. The main coordinator/conversation agent will then deleted the report file inside @temporary/
7. The main coordinator/conversation agent will then launch another swarm/group of agents (the number of agents will be determined dynamically with consideration of the report and the task this group is required to do) to create a restructuring plan based on the report created by the main coordinator/conversation agent.
8. After the restructuring plan is created, the main coordinator/conversation agent will verify the plan itself (using ultrathinking and code-reasoning) and then collaborate with zen to ensure it is correct and does not miss anything.
9. The main coordinator/conversation agent will then launch another swarm/group of agents (the number of agents will be determined dynamically with consideration of the plan and the task this group is required to do) to execute the restructuring plan.

**Important Note:** The zen models might not have access to the individual agent reports that are needed in order to verify the coordinator's identifying areas/report, so it's important that the main coordinator/conversation agent provide the zen MCP agents with all the information or file locations so they could see if anything is missing from the main coordinator's report.

## Phase 3: [Continuation]
